# 部署指南

## Vercel 部署（推荐）

### 1. 准备工作
- 确保代码已推送到 GitHub 仓库
- 注册 Vercel 账号并连接 GitHub

### 2. 部署步骤
1. 登录 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 选择 GitHub 仓库
4. 配置项目设置：
   - Framework Preset: Next.js
   - Root Directory: ./
   - Build Command: `pnpm build`
   - Output Directory: `.next`

### 3. 环境变量配置
在 Vercel 项目设置中添加以下环境变量：
```
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_ENABLE_PWA=true
```

### 4. 自动部署
- Vercel 会自动检测 GitHub 仓库变更
- 每次推送到主分支会自动触发部署
- 支持预览部署（Pull Request）

### 5. 域名配置
1. 在 Vercel 项目设置中添加自定义域名
2. 配置 DNS 记录指向 Vercel
3. 等待 SSL 证书自动配置

## 其他部署选项

### Netlify
1. 连接 GitHub 仓库
2. 构建设置：
   - Build command: `pnpm build && pnpm export`
   - Publish directory: `out`

### 自托管
1. 构建项目：`pnpm build`
2. 启动服务：`pnpm start`
3. 使用 PM2 或 Docker 进行进程管理

## 性能优化建议

### 1. CDN 配置
- 启用 Vercel Edge Network
- 配置静态资源缓存策略

### 2. 监控设置
- 配置 Vercel Analytics
- 设置 Core Web Vitals 监控

### 3. SEO 检查
- 验证 sitemap.xml 可访问
- 检查 robots.txt 配置
- 确认 meta 标签正确

## 部署后检查清单

- [ ] 网站可正常访问
- [ ] HEIC 转换功能正常
- [ ] 拖拽上传工作正常
- [ ] 批量下载功能正常
- [ ] 移动端响应式正常
- [ ] SEO meta 标签正确
- [ ] sitemap.xml 可访问
- [ ] robots.txt 配置正确
- [ ] PWA manifest 正常
- [ ] 性能指标良好（Core Web Vitals）
