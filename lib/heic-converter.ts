// HEIC conversion utility using heic2any library
// Note: In production, you would install and import heic2any
// npm install heic2any

export async function convertHEICToPNG(file: File): Promise<Blob> {
  try {
    // In a real implementation, you would use:
    // import heic2any from 'heic2any'
    // const convertedBlob = await heic2any({
    //   blob: file,
    //   toType: 'image/png',
    //   quality: 1
    // })
    // return convertedBlob as Blob

    // For demo purposes, we'll simulate the conversion
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        // Create a canvas to simulate conversion
        const canvas = document.createElement("canvas")
        canvas.width = 800
        canvas.height = 600
        const ctx = canvas.getContext("2d")!

        // Create a gradient background
        const gradient = ctx.createLinearGradient(0, 0, 800, 600)
        gradient.addColorStop(0, "#f0f9ff")
        gradient.addColorStop(1, "#e0e7ff")
        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, 800, 600)

        // Add some text to indicate conversion
        ctx.fillStyle = "#1f2937"
        ctx.font = "bold 32px Arial"
        ctx.textAlign = "center"
        ctx.fillText("Converted from HEIC", 400, 280)
        ctx.font = "24px Arial"
        ctx.fillText(`Original: ${file.name}`, 400, 320)
        ctx.fillText(`Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 400, 360)

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob)
            } else {
              reject(new Error("Failed to create PNG blob"))
            }
          },
          "image/png",
          1.0,
        )
      }
      reader.onerror = () => reject(new Error("Failed to read file"))
      reader.readAsArrayBuffer(file)
    })
  } catch (error) {
    throw new Error(`Conversion failed: ${error}`)
  }
}

export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}
