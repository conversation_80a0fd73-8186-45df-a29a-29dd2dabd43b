// HEIC conversion utility using heic2any library in main thread

export interface ConversionOptions {
  format: 'png' | 'jpg' | 'jpeg' | 'webp'
  quality?: number
}

export interface ConversionProgress {
  id: string
  progress: number
  message: string
}

export interface ConversionResult {
  id: string
  blob: Blob
  originalSize: number
  convertedSize: number
  format: string
}

// Dynamic import of heic2any
let heic2any: any = null

async function loadHeic2any() {
  if (!heic2any) {
    try {
      const module = await import('heic2any')
      heic2any = module.default || module
    } catch (error) {
      console.error('Failed to load heic2any:', error)
      throw new Error('Failed to load HEIC conversion library')
    }
  }
  return heic2any
}

// Convert HEIC file in main thread with progress simulation
export async function convertHEICFile(
  file: File,
  options: ConversionOptions,
  onProgress?: (progress: ConversionProgress) => void
): Promise<Blob> {
  const id = Math.random().toString(36).substr(2, 9)

  try {
    // Progress: Loading library
    onProgress?.({
      id,
      progress: 10,
      message: 'Loading conversion library...'
    })

    // Load heic2any library
    const converter = await loadHeic2any()

    // Progress: Starting conversion
    onProgress?.({
      id,
      progress: 30,
      message: 'Starting conversion...'
    })

    // Small delay to allow UI update
    await new Promise(resolve => setTimeout(resolve, 100))

    // Progress: Converting
    onProgress?.({
      id,
      progress: 50,
      message: `Converting to ${options.format.toUpperCase()}...`
    })

    // Perform conversion
    const targetFormat = options.format === 'jpg' ? 'jpeg' : options.format
    const conversionOptions: any = {
      blob: file,
      toType: `image/${targetFormat}`,
    }

    // Add quality for lossy formats
    if (targetFormat !== 'png' && options.quality) {
      conversionOptions.quality = options.quality
    }

    const convertedBlob = await converter(conversionOptions)

    // Progress: Finalizing
    onProgress?.({
      id,
      progress: 90,
      message: 'Finalizing...'
    })

    // Handle result (heic2any might return array or single blob)
    const resultBlob = Array.isArray(convertedBlob) ? convertedBlob[0] : convertedBlob

    // Progress: Complete
    onProgress?.({
      id,
      progress: 100,
      message: 'Conversion complete!'
    })

    return resultBlob
  } catch (error) {
    console.error('Conversion failed:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    throw new Error(`Failed to convert HEIC file: ${errorMessage}`)
  }
}

// Legacy function for backward compatibility
export async function convertHEICToPNG(file: File): Promise<Blob> {
  return convertHEICFile(file, { format: 'png' })
}

// Batch conversion function
export async function convertMultipleFiles(
  files: File[],
  options: ConversionOptions,
  onProgress?: (fileIndex: number, progress: ConversionProgress) => void
): Promise<ConversionResult[]> {
  const results: ConversionResult[] = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    try {
      const blob = await convertHEICFile(file, options, (progress) => {
        if (onProgress) {
          onProgress(i, progress)
        }
      })

      results.push({
        id: Math.random().toString(36).substr(2, 9),
        blob,
        originalSize: file.size,
        convertedSize: blob.size,
        format: options.format
      })
    } catch (error) {
      console.error(`Failed to convert file ${file.name}:`, error)
      // Continue with other files even if one fails
    }
  }

  return results
}

// Download single file
export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Download multiple files as ZIP
export async function downloadAsZip(
  files: { blob: Blob; name: string }[],
  zipFilename: string = 'converted-files.zip'
): Promise<void> {
  try {
    // Dynamic import of JSZip
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()

    // Add files to zip
    files.forEach(({ blob, name }) => {
      zip.file(name, blob)
    })

    // Generate zip blob
    const zipBlob = await zip.generateAsync({ type: 'blob' })

    // Download zip file
    downloadBlob(zipBlob, zipFilename)
  } catch (error) {
    console.error('Failed to create ZIP file:', error)
    throw new Error('Failed to create ZIP file. Please try downloading files individually.')
  }
}

// Format file size utility
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

// Validate HEIC file
export function isHEICFile(file: File): boolean {
  const heicExtensions = ['.heic', '.heif']
  const heicMimeTypes = ['image/heic', 'image/heif']

  const hasValidExtension = heicExtensions.some(ext =>
    file.name.toLowerCase().endsWith(ext)
  )
  const hasValidMimeType = heicMimeTypes.includes(file.type.toLowerCase())

  return hasValidExtension || hasValidMimeType
}

// Check browser compatibility
export function checkBrowserCompatibility(): {
  supported: boolean
  missing: string[]
} {
  const missing: string[] = []

  if (typeof Worker === 'undefined') {
    missing.push('Web Workers')
  }

  if (typeof URL.createObjectURL === 'undefined') {
    missing.push('URL.createObjectURL')
  }

  if (typeof FileReader === 'undefined') {
    missing.push('FileReader API')
  }

  return {
    supported: missing.length === 0,
    missing
  }
}
