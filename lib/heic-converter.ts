// HEIC conversion utility using Web Worker and heic2any library

export interface ConversionOptions {
  format: 'png' | 'jpg' | 'jpeg' | 'webp'
  quality?: number
}

export interface ConversionProgress {
  id: string
  progress: number
  message: string
}

export interface ConversionResult {
  id: string
  blob: Blob
  originalSize: number
  convertedSize: number
  format: string
}

// Web Worker instance
let worker: Worker | null = null

// Initialize Web Worker
function initializeWorker(): Worker {
  if (!worker) {
    worker = new Worker('/converter.worker.js')

    // Handle worker errors
    worker.onerror = (error) => {
      console.error('Worker error:', error)
    }
  }
  return worker
}

// Convert HEIC file using Web Worker
export async function convertHEICFile(
  file: File,
  options: ConversionOptions,
  onProgress?: (progress: ConversionProgress) => void
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const worker = initializeWorker()
    const id = Math.random().toString(36).substr(2, 9)

    // Set up message handler
    const handleMessage = (event: MessageEvent) => {
      const { type, payload } = event.data

      switch (type) {
        case 'PROGRESS':
          if (payload.id === id && onProgress) {
            onProgress(payload)
          }
          break

        case 'SUCCESS':
          if (payload.id === id) {
            worker.removeEventListener('message', handleMessage)
            resolve(payload.resultBlob)
          }
          break

        case 'ERROR':
          if (payload.id === id) {
            worker.removeEventListener('message', handleMessage)
            reject(new Error(payload.message))
          }
          break
      }
    }

    worker.addEventListener('message', handleMessage)

    // Send conversion request
    worker.postMessage({
      type: 'CONVERT',
      payload: {
        id,
        sourceFile: file,
        targetFormat: options.format === 'jpg' ? 'jpeg' : options.format,
        quality: options.quality || 0.9
      }
    })
  })
}

// Legacy function for backward compatibility
export async function convertHEICToPNG(file: File): Promise<Blob> {
  return convertHEICFile(file, { format: 'png' })
}

// Batch conversion function
export async function convertMultipleFiles(
  files: File[],
  options: ConversionOptions,
  onProgress?: (fileIndex: number, progress: ConversionProgress) => void
): Promise<ConversionResult[]> {
  const results: ConversionResult[] = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    try {
      const blob = await convertHEICFile(file, options, (progress) => {
        if (onProgress) {
          onProgress(i, progress)
        }
      })

      results.push({
        id: Math.random().toString(36).substr(2, 9),
        blob,
        originalSize: file.size,
        convertedSize: blob.size,
        format: options.format
      })
    } catch (error) {
      console.error(`Failed to convert file ${file.name}:`, error)
      // Continue with other files even if one fails
    }
  }

  return results
}

// Download single file
export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Download multiple files as ZIP
export async function downloadAsZip(
  files: { blob: Blob; name: string }[],
  zipFilename: string = 'converted-files.zip'
): Promise<void> {
  try {
    // Dynamic import of JSZip
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()

    // Add files to zip
    files.forEach(({ blob, name }) => {
      zip.file(name, blob)
    })

    // Generate zip blob
    const zipBlob = await zip.generateAsync({ type: 'blob' })

    // Download zip file
    downloadBlob(zipBlob, zipFilename)
  } catch (error) {
    console.error('Failed to create ZIP file:', error)
    throw new Error('Failed to create ZIP file. Please try downloading files individually.')
  }
}

// Format file size utility
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

// Validate HEIC file
export function isHEICFile(file: File): boolean {
  const heicExtensions = ['.heic', '.heif']
  const heicMimeTypes = ['image/heic', 'image/heif']

  const hasValidExtension = heicExtensions.some(ext =>
    file.name.toLowerCase().endsWith(ext)
  )
  const hasValidMimeType = heicMimeTypes.includes(file.type.toLowerCase())

  return hasValidExtension || hasValidMimeType
}

// Check browser compatibility
export function checkBrowserCompatibility(): {
  supported: boolean
  missing: string[]
} {
  const missing: string[] = []

  if (typeof Worker === 'undefined') {
    missing.push('Web Workers')
  }

  if (typeof URL.createObjectURL === 'undefined') {
    missing.push('URL.createObjectURL')
  }

  if (typeof FileReader === 'undefined') {
    missing.push('FileReader API')
  }

  return {
    supported: missing.length === 0,
    missing
  }
}

export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}
