// HEIC Converter Web Worker
// This worker handles HEIC conversion in the background to avoid blocking the main UI thread

// Import heic2any library (will be available after npm install)
// Note: We'll use dynamic import to handle the library loading
let heic2any = null;

// Initialize the heic2any library
async function initializeHeic2any() {
  if (!heic2any) {
    try {
      // Dynamic import of heic2any
      const module = await import('heic2any');
      heic2any = module.default || module;
    } catch (error) {
      throw new Error('Failed to load heic2any library: ' + error.message);
    }
  }
  return heic2any;
}

// Main message handler
self.onmessage = async function(event) {
  const { type, payload } = event.data;
  
  try {
    switch (type) {
      case 'CONVERT':
        await handleConversion(payload);
        break;
      case 'PING':
        self.postMessage({ type: 'PONG' });
        break;
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      payload: {
        id: payload?.id,
        message: error.message,
        stack: error.stack
      }
    });
  }
};

// Handle file conversion
async function handleConversion(payload) {
  const { id, sourceFile, targetFormat, quality } = payload;
  
  // Validate input
  if (!sourceFile || !targetFormat) {
    throw new Error('Missing required parameters: sourceFile and targetFormat');
  }
  
  // Send progress update
  self.postMessage({
    type: 'PROGRESS',
    payload: { id, progress: 10, message: 'Initializing conversion...' }
  });
  
  // Initialize heic2any library
  await initializeHeic2any();
  
  self.postMessage({
    type: 'PROGRESS',
    payload: { id, progress: 30, message: 'Loading HEIC file...' }
  });
  
  // Convert the file
  const conversionOptions = {
    blob: sourceFile,
    toType: `image/${targetFormat}`,
    quality: quality || 0.9
  };
  
  // Add format-specific options
  if (targetFormat === 'jpeg' || targetFormat === 'jpg') {
    conversionOptions.quality = quality || 0.9;
  } else if (targetFormat === 'webp') {
    conversionOptions.quality = quality || 0.9;
  }
  // PNG is lossless, so no quality setting needed
  
  self.postMessage({
    type: 'PROGRESS',
    payload: { id, progress: 50, message: 'Converting HEIC to ' + targetFormat.toUpperCase() + '...' }
  });
  
  try {
    const convertedBlob = await heic2any(conversionOptions);
    
    self.postMessage({
      type: 'PROGRESS',
      payload: { id, progress: 90, message: 'Finalizing conversion...' }
    });
    
    // Handle the result (heic2any might return an array or single blob)
    const resultBlob = Array.isArray(convertedBlob) ? convertedBlob[0] : convertedBlob;
    
    self.postMessage({
      type: 'SUCCESS',
      payload: {
        id,
        resultBlob,
        originalSize: sourceFile.size,
        convertedSize: resultBlob.size,
        format: targetFormat
      }
    });
    
  } catch (conversionError) {
    throw new Error(`Conversion failed: ${conversionError.message}`);
  }
}

// Error handler for uncaught errors
self.onerror = function(error) {
  self.postMessage({
    type: 'ERROR',
    payload: {
      message: 'Worker error: ' + error.message,
      filename: error.filename,
      lineno: error.lineno
    }
  });
};

// Handle unhandled promise rejections
self.onunhandledrejection = function(event) {
  self.postMessage({
    type: 'ERROR',
    payload: {
      message: 'Unhandled promise rejection: ' + event.reason
    }
  });
};

// Send ready signal
self.postMessage({ type: 'READY' });
