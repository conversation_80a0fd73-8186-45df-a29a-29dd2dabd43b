// Installation script for HEIC converter dependencies
import { execSync } from "child_process"

console.log("Installing HEIC converter dependencies...")

try {
  // Install heic2any for HEIC conversion
  execSync("npm install heic2any", { stdio: "inherit" })

  // Install additional image processing libraries
  execSync("npm install file-saver", { stdio: "inherit" })

  console.log("✅ Dependencies installed successfully!")
  console.log("📝 Note: heic2any library enables client-side HEIC to PNG conversion")
} catch (error) {
  console.error("❌ Error installing dependencies:", error.message)
}
