# HEIC to PNG Converter

一个专业的在线HEIC转PNG工具，支持批量转换、拖拽上传，完全在浏览器中运行，保护用户隐私。

## ✨ 功能特点

- 🔄 **多格式支持**: HEIC转PNG、JPG、WEBP
- 📁 **批量转换**: 支持同时转换多个文件
- 🖱️ **拖拽上传**: 直观的拖拽界面
- 🔒 **隐私保护**: 文件在浏览器本地处理，不上传服务器
- 📱 **移动端友好**: 响应式设计，支持所有设备
- ⚡ **高性能**: 基于WebAssembly的快速转换
- 📊 **转换历史**: 本地存储转换记录
- 🎯 **SEO优化**: 完整的搜索引擎优化

## 🚀 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI
- **图像处理**: heic2any
- **构建工具**: pnpm

## 📦 安装和运行

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd heic-converter

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 生产构建

```bash
# 构建项目
pnpm build

# 启动生产服务器
pnpm start
```

## 🌐 部署

### Vercel 部署（推荐）

1. 推送代码到 GitHub
2. 在 [Vercel](https://vercel.com) 导入项目
3. 配置环境变量（可选）
4. 自动部署完成

详细部署指南请查看 [docs/deployment.md](docs/deployment.md)

## 📁 项目结构

```
├── app/                    # Next.js App Router
│   ├── page.tsx           # 主页面
│   ├── layout.tsx         # 布局组件
│   ├── sitemap.ts         # 站点地图
│   ├── robots.ts          # 爬虫规则
│   └── manifest.ts        # PWA配置
├── components/            # UI组件
├── lib/                   # 工具函数
│   └── heic-converter.ts  # 转换核心逻辑
├── docs/                  # 文档
└── public/               # 静态资源
```

## 🎯 SEO优化

- ✅ 结构化数据 (JSON-LD)
- ✅ XML Sitemap
- ✅ Robots.txt
- ✅ Open Graph 标签
- ✅ Twitter Cards
- ✅ PWA Manifest
- ✅ 性能优化
- ✅ 移动端优化

## 📊 性能指标

- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
