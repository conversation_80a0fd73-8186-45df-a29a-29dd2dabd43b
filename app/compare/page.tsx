import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowRight, BarChart3, FileImage, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'Image Format Comparisons - HEIC vs JPG vs PNG Analysis',
  description: 'Compare image formats with detailed analysis. Learn the differences between HEIC, JPG, PNG and choose the right format for your needs.',
  keywords: 'image format comparison, heic vs jpg, png vs jpg, heic vs png, image format guide',
  openGraph: {
    title: 'Image Format Comparisons - HEIC vs JPG vs PNG',
    description: 'Detailed comparisons of image formats to help you choose the right one.',
    type: 'website',
    url: 'https://convert-heic.com/compare',
  },
}

const comparisons = [
  {
    title: 'HEIC vs. JPG vs. PNG: 2025\'s Most Comprehensive Format Comparison',
    description: 'Complete comparison of the three major image formats. Learn compression, quality, compatibility, and use cases to choose the right format for your needs.',
    href: '/compare/heic-vs-jpg-png',
    category: 'Format Comparison',
    readTime: '10 min read',
    featured: true,
    tags: ['HEIC', 'JPG', 'PNG', 'Compression', 'Quality']
  }
]

export default function ComparePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">H</span>
              </div>
              <span className="text-xl font-bold text-gray-900">HEIC to PNG</span>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-gray-600 hover:text-blue-600 transition-colors">
                Converter
              </Link>
              <Link href="/guides" className="text-gray-600 hover:text-blue-600 transition-colors">
                Guides
              </Link>
              <Link href="/compare" className="text-blue-600 font-medium">
                Compare
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 py-3 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">Home</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Compare</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Image Format Comparisons
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Make informed decisions about image formats with our detailed comparisons. 
            Understand the technical differences, use cases, and trade-offs between different formats.
          </p>
        </div>

        {/* Featured Comparison */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Comparison</h2>
          {comparisons.filter(comp => comp.featured).map((comparison, index) => (
            <article key={index} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
              <div className="p-8">
                <div className="flex items-center justify-between mb-4">
                  <Badge variant="secondary">{comparison.category}</Badge>
                  <span className="text-sm text-gray-500 flex items-center">
                    <BarChart3 className="w-4 h-4 mr-1" />
                    {comparison.readTime}
                  </span>
                </div>
                
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                  {comparison.title}
                </h3>
                
                <p className="text-gray-600 mb-6 text-lg">
                  {comparison.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {comparison.tags.map((tag, tagIndex) => (
                    <Badge key={tagIndex} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <Link href={comparison.href}>
                  <Button size="lg" className="group">
                    Read Comparison
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* Coming Soon */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">More Comparisons Coming Soon</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 text-center">
              <FileImage className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">HEIC vs WEBP</h3>
              <p className="text-gray-500 text-sm">Modern format comparison</p>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 text-center">
              <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Compression Analysis</h3>
              <p className="text-gray-500 text-sm">File size vs quality comparison</p>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 text-center">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Browser Support</h3>
              <p className="text-gray-500 text-sm">Compatibility across platforms</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-8 text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Ready to Convert Your Images?</h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Now that you understand the differences, use our converter to transform your images to the perfect format.
          </p>
          <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
            <Link href="/">Start Converting Now</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
