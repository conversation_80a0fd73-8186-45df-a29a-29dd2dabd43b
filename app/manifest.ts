import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free',
    short_name: 'HEIC to PNG',
    description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    icons: [
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    categories: ['utilities', 'productivity'],
    lang: 'en',
    orientation: 'portrait-primary',
  }
}
