import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free | Fast & Secure',
  description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser. Batch convert iPhone photos to PNG format instantly.',
  keywords: 'heic to png, convert heic to png, heic to png converter, heic converter, iphone photos to png, heic to png online',
  authors: [{ name: 'HEIC to PNG Converter' }],
  creator: 'HEIC to PNG Converter',
  publisher: 'HEIC to PNG Converter',
  robots: 'index, follow',
  openGraph: {
    title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free',
    description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.',
    type: 'website',
    locale: 'en_US',
    siteName: 'HEIC to PNG Converter',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free',
    description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.',
    creator: '@heictopng',
  },
  alternates: {
    canonical: 'https://convert-heic.com',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        {/* Performance optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

        {/* Preload critical resources */}
        <link rel="preload" href="/converter.worker.js" as="script" />

        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <meta name="theme-color" content="#3b82f6" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>
      <body>{children}</body>
    </html>
  )
}
