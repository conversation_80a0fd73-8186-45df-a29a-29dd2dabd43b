import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free | Fast & Secure',
  description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser. Batch convert iPhone photos to PNG format instantly.',
  keywords: 'heic to png, convert heic to png, heic to png converter, heic converter, iphone photos to png, heic to png online',
  openGraph: {
    title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free',
    description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'HEIC to PNG Converter - Convert HEIC to PNG Online Free',
    description: 'Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
