import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Navigation } from '@/components/navigation'
import { Footer } from '@/components/footer'

export const metadata: Metadata = {
  title: 'Terms of Service - HEIC to PNG Converter',
  description: 'Terms of service for HEIC to PNG converter. Understand the terms and conditions for using our service.',
  robots: 'noindex, nofollow',
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Navigation />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="ghost" asChild className="text-gray-600 hover:text-gray-900">
            <Link href="/" className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </Link>
          </Button>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Terms of Service</h1>
          <p className="text-gray-600 mb-8">Last updated: January 11, 2025</p>

          <div className="prose prose-lg max-w-none">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Acceptance of Terms</h2>
            <p className="text-gray-700 mb-6">
              By using HEIC to PNG Converter, you agree to these terms of service. If you do not agree, please do not use our service.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Service Description</h2>
            <p className="text-gray-700 mb-6">
              HEIC to PNG Converter is a free online tool that converts HEIC image files to PNG, JPG, and other formats entirely in your browser.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">User Responsibilities</h2>
            <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
              <li>You are responsible for the content of files you convert</li>
              <li>You must not use the service for illegal purposes</li>
              <li>You must not attempt to harm or disrupt the service</li>
              <li>You must respect intellectual property rights</li>
            </ul>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Service Availability</h2>
            <p className="text-gray-700 mb-6">
              We strive to maintain high availability but cannot guarantee uninterrupted service. The service is provided "as is" without warranties.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Privacy and Data</h2>
            <p className="text-gray-700 mb-6">
              All file processing occurs locally in your browser. We do not store or have access to your files. See our Privacy Policy for details.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Limitation of Liability</h2>
            <p className="text-gray-700 mb-6">
              We are not liable for any damages arising from the use of our service. Use the service at your own risk.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Changes to Terms</h2>
            <p className="text-gray-700 mb-6">
              We may update these terms from time to time. Continued use of the service constitutes acceptance of updated terms.
            </p>

            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Contact</h2>
            <p className="text-gray-700">
              If you have questions about these terms, please contact us through our website.
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
