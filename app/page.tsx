"use client"

import type React from "react"

import { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react"
import {
  Upload,
  Download,
  CheckCircle,
  Zap,
  Shield,
  Star,
  FileImage,
  ArrowRight,
  Sparkles,
  Lock,
  Gauge,
  Clock,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { convertHEICFile, downloadAsZip, formatFileSize, isHEICFile } from "@/lib/heic-converter"

interface ConversionHistory {
  id: string
  fileName: string
  originalSize: number
  convertedSize: number
  format: string
  timestamp: number
  conversionTime: number
}

interface ConvertedFile {
  id: string
  name: string
  originalSize: number
  convertedSize: number
  url: string
  status: "pending" | "converting" | "completed" | "error"
  progress: number
  errorMessage?: string
  conversionTime?: number
}

// JSON-LD structured data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "HEIC to PNG Converter",
  "description": "Convert HEIC to PNG online for free. Fast, secure HEIC to PNG converter that works in your browser.",
  "url": "https://convert-heic.com",
  "applicationCategory": "UtilitiesApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "featureList": [
    "Convert HEIC to PNG",
    "Convert HEIC to JPG",
    "Convert HEIC to WEBP",
    "Batch conversion",
    "No file upload required",
    "Privacy protection"
  ],
  "screenshot": "https://convert-heic.com/screenshot.png",
  "softwareVersion": "1.0",
  "author": {
    "@type": "Organization",
    "name": "HEIC to PNG Converter"
  },
  "provider": {
    "@type": "Organization",
    "name": "HEIC to PNG Converter"
  }
}

export default function HEICConverter() {
  const [files, setFiles] = useState<ConvertedFile[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [outputFormat, setOutputFormat] = useState<'png' | 'jpg' | 'webp'>('png')
  const [quality, setQuality] = useState(90)
  const [history, setHistory] = useState<ConversionHistory[]>([])
  const [showHistory, setShowHistory] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Load history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('heic-conversion-history')
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory))
      } catch (error) {
        console.error('Failed to load conversion history:', error)
      }
    }
  }, [])

  // Save history to localStorage
  const saveToHistory = (file: ConvertedFile) => {
    if (file.status === 'completed' && file.conversionTime) {
      const historyItem: ConversionHistory = {
        id: Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        originalSize: file.originalSize,
        convertedSize: file.convertedSize,
        format: outputFormat,
        timestamp: Date.now(),
        conversionTime: file.conversionTime
      }

      const newHistory = [historyItem, ...history].slice(0, 50) // Keep last 50 conversions
      setHistory(newHistory)
      localStorage.setItem('heic-conversion-history', JSON.stringify(newHistory))
    }
  }

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    // Only set dragging to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false)
    }
  }, [])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }, [])

  const convertFile = async (file: File, id: string): Promise<Blob> => {
    try {
      const blob = await convertHEICFile(
        file,
        {
          format: outputFormat,
          quality: outputFormat !== 'png' ? quality / 100 : undefined
        },
        (progress) => {
          setFiles((prev) => prev.map((f) =>
            f.id === id ? { ...f, progress: progress.progress } : f
          ))
        }
      )
      return blob
    } catch (error) {
      console.error('Conversion failed:', error)
      throw error
    }
  }

  const handleFiles = async (fileList: FileList) => {
    const heicFiles = Array.from(fileList).filter(isHEICFile)

    if (heicFiles.length === 0) {
      alert("Please select HEIC files to convert.")
      return
    }

    // Check file size limits (50MB per file)
    const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB in bytes
    const oversizedFiles = heicFiles.filter(file => file.size > MAX_FILE_SIZE)

    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(f => f.name).join(', ')
      alert(`The following files exceed the 50MB limit and will be skipped: ${fileNames}`)
    }

    const validFiles = heicFiles.filter(file => file.size <= MAX_FILE_SIZE)

    if (validFiles.length === 0) {
      alert("No valid files to convert. Please ensure files are under 50MB.")
      return
    }

    setIsConverting(true)

    const newFiles: ConvertedFile[] = validFiles.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      originalSize: file.size,
      convertedSize: 0,
      url: "",
      status: "converting" as const,
      progress: 0,
    }))

    setFiles((prev) => [...prev, ...newFiles])

    // Convert files
    for (let i = 0; i < validFiles.length; i++) {
      const startTime = Date.now()
      try {
        const convertedBlob = await convertFile(validFiles[i], newFiles[i].id)
        const url = URL.createObjectURL(convertedBlob)
        const conversionTime = Date.now() - startTime

        setFiles((prev) =>
          prev.map((file) => {
            if (file.id === newFiles[i].id) {
              const updatedFile = {
                ...file,
                convertedSize: convertedBlob.size,
                url,
                status: "completed" as const,
                progress: 100,
                conversionTime
              }
              // Save to history
              saveToHistory(updatedFile)
              return updatedFile
            }
            return file
          }),
        )
      } catch (error) {
        console.error('Conversion error:', error)
        const errorMessage = error instanceof Error ? error.message : 'Unknown conversion error'
        setFiles((prev) => prev.map((file) =>
          file.id === newFiles[i].id
            ? { ...file, status: "error", errorMessage, progress: 0 }
            : file
        ))
      }
    }

    setIsConverting(false)
  }

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    handleFiles(e.dataTransfer.files)
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files)
    }
  }

  const downloadFile = (file: ConvertedFile) => {
    const a = document.createElement("a")
    a.href = file.url
    const extension = outputFormat === 'jpg' ? 'jpg' : outputFormat
    a.download = file.name.replace(/\.(heic|heif|jpg|jpeg)$/i, `.${extension}`)
    a.click()
  }

  const downloadAll = async () => {
    const completedFiles = files.filter((f) => f.status === "completed")

    if (completedFiles.length === 0) return

    if (completedFiles.length === 1) {
      downloadFile(completedFiles[0])
      return
    }

    try {
      // Prepare files for ZIP download
      const zipFiles = await Promise.all(
        completedFiles.map(async (file) => {
          const response = await fetch(file.url)
          const blob = await response.blob()
          const extension = outputFormat === 'jpg' ? 'jpg' : outputFormat
          const name = file.name.replace(/\.(heic|heif|jpg|jpeg)$/i, `.${extension}`)
          return { blob, name }
        })
      )

      await downloadAsZip(zipFiles, `heic-to-${outputFormat}-converted.zip`)
    } catch (error) {
      console.error('ZIP download failed:', error)
      // Fallback to individual downloads
      completedFiles.forEach(downloadFile)
    }
  }

  const clearAll = () => {
    files.forEach((file) => {
      if (file.url) URL.revokeObjectURL(file.url)
    })
    setFiles([])
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FileImage className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2 h-2 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  HEIC to PNG Converter
                </h1>
                <p className="text-xs text-gray-500">Convert HEIC to PNG Online</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Features
                </a>
                <a href="#how-it-works" className="text-gray-600 hover:text-gray-900 transition-colors">
                  How it Works
                </a>
                <a href="#faq" className="text-gray-600 hover:text-gray-900 transition-colors">
                  FAQ
                </a>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistory(!showHistory)}
                className="bg-transparent"
              >
                <Clock className="w-4 h-4 mr-2" />
                History ({history.length})
              </Button>
              <Button variant="outline" size="sm" className="hidden sm:flex bg-transparent">
                Need Help?
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
              >
                Try Pro Free
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb Navigation */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 py-3 text-sm">
            <a href="/" className="text-blue-600 hover:text-blue-800">Home</a>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">HEIC to PNG Converter</span>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-indigo-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-12">
          {/* Trust Indicators */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-8 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="flex -space-x-1">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 border-2 border-white"
                    ></div>
                  ))}
                </div>
                <span>2M+ users trust us</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span>4.9/5 rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-green-600" />
                <span>100% secure</span>
              </div>
            </div>
          </div>

          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4 px-4 py-2 bg-blue-50 text-blue-700 border-blue-200">
              <Sparkles className="w-4 h-4 mr-2" />
              Best HEIC to PNG Converter
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Convert HEIC to PNG
              <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Instantly & Securely
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform your iPhone HEIC photos to universal PNG format with our advanced HEIC to PNG converter. Lightning-fast
              processing, pixel-perfect quality, and enterprise-grade security for all your HEIC to PNG conversions.
            </p>

            {/* Feature Pills */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Zap className="w-4 h-4 mr-2 text-yellow-500" />
                Lightning Fast
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Lock className="w-4 h-4 mr-2 text-green-500" />
                100% Private
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Gauge className="w-4 h-4 mr-2 text-blue-500" />
                Batch Processing
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <CheckCircle className="w-4 h-4 mr-2 text-purple-500" />
                No Registration
              </Badge>
            </div>
          </div>

          {/* Converter Tool */}
          <Card className="max-w-5xl mx-auto shadow-2xl border-0 bg-white/70 backdrop-blur-md">
            <CardContent className="p-8">
              {/* Conversion Settings */}
              <div className="mb-8 p-4 sm:p-6 bg-gray-50/50 rounded-xl border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Conversion Settings</h3>
                <div className="grid sm:grid-cols-2 gap-4 sm:gap-6">
                  {/* Output Format Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="format-select" className="text-sm font-medium text-gray-700">
                      Output Format
                    </Label>
                    <Select value={outputFormat} onValueChange={(value: 'png' | 'jpg' | 'webp') => setOutputFormat(value)}>
                      <SelectTrigger id="format-select" className="w-full">
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="png">PNG (Lossless, Best Quality)</SelectItem>
                        <SelectItem value="jpg">JPG (Smaller Size, Good Quality)</SelectItem>
                        <SelectItem value="webp">WebP (Modern, Balanced)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Quality Control */}
                  {outputFormat !== 'png' && (
                    <div className="space-y-2">
                      <Label htmlFor="quality-slider" className="text-sm font-medium text-gray-700">
                        Quality: {quality}%
                      </Label>
                      <Slider
                        id="quality-slider"
                        min={10}
                        max={100}
                        step={5}
                        value={[quality]}
                        onValueChange={(value) => setQuality(value[0])}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Smaller file</span>
                        <span>Better quality</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div
                className={`relative border-2 border-dashed rounded-2xl p-6 sm:p-12 text-center transition-all duration-300 ${
                  isDragging
                    ? "border-blue-500 bg-blue-50/70 scale-[1.02] shadow-lg shadow-blue-500/20"
                    : "border-gray-300 hover:border-gray-400 hover:bg-gray-50/50 hover:scale-[1.01]"
                }`}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Upload className="w-10 h-10 text-white" />
                  </div>
                  <h3 className={`text-2xl font-bold mb-3 transition-colors duration-300 ${
                    isDragging ? "text-blue-600" : "text-gray-900"
                  }`}>
                    {isDragging ? "Release to upload HEIC files" : "Drop your HEIC files here"}
                  </h3>
                  <p className={`mb-6 text-lg transition-colors duration-300 ${
                    isDragging ? "text-blue-600" : "text-gray-600"
                  }`}>
                    {isDragging
                      ? `Convert to ${outputFormat.toUpperCase()} format instantly`
                      : "Convert HEIC to PNG instantly - click to browse from your device"
                    }
                  </p>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".heic,.heif,image/heic,image/heif"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-input"
                  />

                  <Button
                    asChild
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <label htmlFor="file-input" className="cursor-pointer">
                      <FileImage className="w-5 h-5 mr-2" />
                      Choose HEIC Files
                    </label>
                  </Button>

                  <p className="text-sm text-gray-500 mt-4">
                    Supports: HEIC, HEIF • <span className="font-medium text-orange-600">Max size: 50MB per file</span> • Batch processing available • Convert to {outputFormat.toUpperCase()}
                  </p>
                </div>
              </div>

              {files.length > 0 && (
                <div className="mt-8 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-xl font-semibold text-gray-900">Conversion Progress</h4>
                    <div className="flex space-x-3">
                      {files.some((f) => f.status === "completed") && (
                        <Button onClick={downloadAll} className="bg-green-600 hover:bg-green-700">
                          <Download className="w-4 h-4 mr-2" />
                          Download All
                        </Button>
                      )}
                      <Button variant="outline" onClick={clearAll} size="sm">
                        Clear All
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {files.map((file) => (
                      <Card key={file.id} className="border border-gray-200 bg-white/50 backdrop-blur-sm">
                        <CardContent className="p-3 sm:p-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                                  <FileImage className="w-5 h-5 text-gray-600" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium text-gray-900 truncate">{file.name}</p>
                                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-gray-500">
                                    <span>{(file.originalSize / 1024 / 1024).toFixed(2)} MB</span>
                                    {file.convertedSize > 0 && (
                                      <>
                                        <ArrowRight className="w-3 h-3 hidden sm:block" />
                                        <span className="text-green-600 font-medium">
                                          {(file.convertedSize / 1024 / 1024).toFixed(2)} MB {outputFormat.toUpperCase()}
                                        </span>
                                      </>
                                    )}
                                  </div>
                                  {file.status === "converting" && (
                                    <div className="mt-2">
                                      <Progress value={file.progress} className="h-2" />
                                      <p className="text-xs text-gray-500 mt-1">
                                        {Math.round(file.progress)}% complete
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-3">
                              {file.status === "converting" && (
                                <div className="flex items-center space-x-2 text-blue-600">
                                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                  <span className="text-sm font-medium">Converting...</span>
                                </div>
                              )}
                              {file.status === "completed" && (
                                <div className="space-y-2">
                                  <div className="flex items-center space-x-3">
                                    <div className="flex items-center space-x-2 text-green-600">
                                      <CheckCircle className="w-5 h-5" />
                                      <span className="text-sm font-medium">Complete</span>
                                    </div>
                                    <Button
                                      onClick={() => downloadFile(file)}
                                      size="sm"
                                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                                    >
                                      <Download className="w-4 h-4 mr-2" />
                                      Download
                                    </Button>
                                  </div>
                                  {file.conversionTime && (
                                    <p className="text-xs text-gray-500">
                                      Converted in {(file.conversionTime / 1000).toFixed(1)}s
                                    </p>
                                  )}
                                </div>
                              )}
                              {file.status === "error" && (
                                <div className="space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-red-600 text-sm font-medium">Conversion failed</span>
                                    <Button
                                      onClick={() => {
                                        // Reset file status for retry
                                        setFiles(prev => prev.map(f =>
                                          f.id === file.id
                                            ? { ...f, status: "pending", progress: 0, errorMessage: undefined }
                                            : f
                                        ))
                                        // Note: User needs to re-upload the file for retry
                                        alert('Please re-upload the file to retry conversion.')
                                      }}
                                      size="sm"
                                      variant="outline"
                                      className="h-6 px-2 text-xs"
                                    >
                                      Retry
                                    </Button>
                                  </div>
                                  {file.errorMessage && (
                                    <p className="text-red-500 text-xs">{file.errorMessage}</p>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* History Panel */}
          {showHistory && (
            <Card className="max-w-5xl mx-auto mt-8 shadow-2xl border-0 bg-white/70 backdrop-blur-md">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Conversion History</h3>
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setHistory([])
                        localStorage.removeItem('heic-conversion-history')
                      }}
                    >
                      Clear History
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHistory(false)}
                    >
                      Close
                    </Button>
                  </div>
                </div>

                {history.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No conversion history yet</p>
                    <p className="text-sm">Your conversion history will appear here</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {history.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50/50 rounded-lg border border-gray-200">
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 truncate">{item.fileName}</p>
                          <div className="flex flex-wrap items-center gap-2 text-sm text-gray-500">
                            <span>{(item.originalSize / 1024 / 1024).toFixed(2)} MB</span>
                            <ArrowRight className="w-3 h-3" />
                            <span className="text-green-600 font-medium">
                              {(item.convertedSize / 1024 / 1024).toFixed(2)} MB {item.format.toUpperCase()}
                            </span>
                            <span>•</span>
                            <span>{(item.conversionTime / 1000).toFixed(1)}s</span>
                          </div>
                        </div>
                        <div className="text-right text-sm text-gray-500">
                          <p>{new Date(item.timestamp).toLocaleDateString()}</p>
                          <p>{new Date(item.timestamp).toLocaleTimeString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">2M+</div>
              <div className="text-gray-600">Files Converted</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">99.9%</div>
              <div className="text-gray-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">Less than 30 seconds</div>
              <div className="text-gray-600">Average Time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">100%</div>
              <div className="text-gray-600">Privacy Protected</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 px-4 py-2 bg-indigo-50 text-indigo-700 border-indigo-200">
              <Star className="w-4 h-4 mr-2" />
              Premium Features
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Best HEIC to PNG Converter Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the most advanced HEIC to PNG conversion with enterprise-grade features and perfect quality
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Zap className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Lightning Fast Processing</CardTitle>
                <CardDescription className="text-gray-600">
                  Advanced algorithms ensure your files are converted in seconds, not minutes. Batch process up to 100
                  files simultaneously.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Shield className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Bank-Level Security</CardTitle>
                <CardDescription className="text-gray-600">
                  All processing happens in your browser. Your files never touch our servers, ensuring complete privacy
                  and security.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-pink-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Sparkles className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Perfect Quality</CardTitle>
                <CardDescription className="text-gray-600">
                  Maintain original image quality with our lossless conversion process. No compression artifacts or
                  quality degradation.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600">Simple, fast, and secure conversion in three steps</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-1/2 left-1/3 w-1/3 h-0.5 bg-gradient-to-r from-blue-300 to-indigo-300 transform -translate-y-1/2"></div>
            <div className="hidden md:block absolute top-1/2 right-1/3 w-1/3 h-0.5 bg-gradient-to-r from-indigo-300 to-purple-300 transform -translate-y-1/2"></div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                1
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Upload Your Files</h3>
              <p className="text-gray-600 text-lg">
                Drag and drop your HEIC files or click to browse. Support for multiple files and batch processing.
              </p>
            </div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-purple-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                2
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Instant Conversion</h3>
              <p className="text-gray-600 text-lg">
                Our advanced engine processes your files locally in your browser with real-time progress tracking.
              </p>
            </div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                3
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Download Results</h3>
              <p className="text-gray-600 text-lg">
                Download your converted PNG files individually or all at once. Perfect quality guaranteed.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About HEIC Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">About HEIC to PNG Conversion</h2>
            <p className="text-lg text-gray-600">
              Learn everything about converting HEIC files to PNG format
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">What is HEIC Format?</h3>
              <p className="text-gray-600 mb-4">
                HEIC (High Efficiency Image Container) is Apple's modern image format used by iPhones since iOS 11.
                It offers better compression than JPEG while maintaining higher quality, but has limited compatibility
                with non-Apple devices and software.
              </p>
              <p className="text-gray-600">
                Converting HEIC to PNG ensures universal compatibility across all devices and platforms.
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Why Convert HEIC to PNG?</h3>
              <ul className="text-gray-600 space-y-2">
                <li>• <strong>Universal Compatibility:</strong> PNG works on all devices and software</li>
                <li>• <strong>Lossless Quality:</strong> No compression artifacts or quality loss</li>
                <li>• <strong>Transparency Support:</strong> PNG supports transparent backgrounds</li>
                <li>• <strong>Web-Friendly:</strong> Perfect for websites and online sharing</li>
                <li>• <strong>Professional Use:</strong> Ideal for design and editing software</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">Everything you need to know about HEIC to PNG conversion</p>
          </div>

          <Accordion type="single" collapsible className="space-y-4">
            <AccordionItem value="item-1" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">What makes your HEIC to PNG converter better than others?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Our HEIC to PNG converter offers superior speed, security, and quality. We process HEIC files locally in your browser
                (never on our servers), support batch HEIC to PNG conversion of up to 100 files, and maintain perfect PNG image quality
                with our advanced algorithms. Plus, it's completely free with no registration required.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Is my data safe when using your converter?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Your files are processed entirely in your browser using JavaScript. They never leave your device or get
                uploaded to our servers. This ensures complete privacy and security for your personal photos and
                sensitive images.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">What file sizes and formats do you support?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                We support HEIC and HEIF files up to 50MB each. You can convert multiple files simultaneously with our
                batch processing feature. The output is always high-quality PNG format with lossless compression.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">How to convert HEIC to PNG for free?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Converting HEIC to PNG is simple and free with our online tool. Just drag and drop your HEIC files onto our converter,
                select PNG as the output format, and click convert. Your HEIC files will be converted to PNG format instantly without
                any cost or registration required.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Why convert HEIC to PNG instead of JPG?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                PNG format offers several advantages over JPG when converting from HEIC: lossless compression maintains perfect image quality,
                supports transparency, and provides better compatibility with design software. PNG is ideal for images that need to maintain
                their original quality without any compression artifacts.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Can I convert multiple HEIC files to PNG at once?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Yes! Our HEIC to PNG converter supports batch conversion. You can select multiple HEIC files and convert them all to PNG
                format simultaneously. After conversion, you can download all PNG files as a single ZIP archive for convenience.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-7" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Do I need to install any software?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                No installation required! Our HEIC to PNG converter works entirely in your web browser. Simply visit our website and
                start converting immediately. It works on all modern browsers including Chrome, Firefox, Safari, and Edge.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Will converting to PNG affect image quality?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                PNG is a lossless format, so there's no quality degradation during conversion. However, PNG files are
                typically larger than HEIC files due to different compression methods. You'll get the same visual
                quality with universal compatibility.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-indigo-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-4">Ready to Convert HEIC to PNG?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Join millions of users who trust our secure, fast, and reliable HEIC to PNG converter
          </p>
          <Button
            size="lg"
            className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-5 h-5 mr-2" />
            Start Converting Now
          </Button>
          <p className="text-sm text-blue-200 mt-4">No registration • No limits • 100% free</p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <FileImage className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">HEIC to PNG Converter</h3>
                  <p className="text-sm text-gray-400">Convert HEIC to PNG Online</p>
                </div>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                The most trusted and secure HEIC to PNG converter. Used by millions of professionals and individuals
                worldwide for fast, reliable image conversion.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Tools</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">HEIC to PNG</li>
                <li className="hover:text-white transition-colors cursor-pointer">HEIC to JPG</li>
                <li className="hover:text-white transition-colors cursor-pointer">PNG to JPG</li>
                <li className="hover:text-white transition-colors cursor-pointer">Image Compressor</li>
                <li className="hover:text-white transition-colors cursor-pointer">Batch Converter</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Support</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">Help Center</li>
                <li className="hover:text-white transition-colors cursor-pointer">Contact Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">API Documentation</li>
                <li className="hover:text-white transition-colors cursor-pointer">Status Page</li>
                <li className="hover:text-white transition-colors cursor-pointer">Bug Reports</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Company</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">About Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">Privacy Policy</li>
                <li className="hover:text-white transition-colors cursor-pointer">Terms of Service</li>
                <li className="hover:text-white transition-colors cursor-pointer">Cookie Policy</li>
                <li className="hover:text-white transition-colors cursor-pointer">Careers</li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-gray-800" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2024 HEIC to PNG Converter. All rights reserved.</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0 text-sm text-gray-400">
              <span>🔒 SSL Secured</span>
              <span>⚡ 99.9% Uptime</span>
              <span>🌍 Global CDN</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
