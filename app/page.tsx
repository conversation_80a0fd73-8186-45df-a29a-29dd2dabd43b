"use client"

import type React from "react"

import { useState, useCallback, useRef } from "react"
import {
  Upload,
  Download,
  CheckCircle,
  Zap,
  Shield,
  Star,
  FileImage,
  ArrowRight,
  Sparkles,
  Lock,
  Gauge,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

interface ConvertedFile {
  id: string
  name: string
  originalSize: number
  convertedSize: number
  url: string
  status: "converting" | "completed" | "error"
  progress: number
}

export default function HEICConverter() {
  const [files, setFiles] = useState<ConvertedFile[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const convertHEICToPNG = async (file: File, id: string): Promise<Blob> => {
    return new Promise((resolve) => {
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 15
        if (progress > 100) progress = 100

        setFiles((prev) => prev.map((f) => (f.id === id ? { ...f, progress } : f)))

        if (progress >= 100) {
          clearInterval(interval)
          // Create mock converted image
          const canvas = document.createElement("canvas")
          canvas.width = 1200
          canvas.height = 800
          const ctx = canvas.getContext("2d")!

          // Create gradient background
          const gradient = ctx.createLinearGradient(0, 0, 1200, 800)
          gradient.addColorStop(0, "#667eea")
          gradient.addColorStop(1, "#764ba2")
          ctx.fillStyle = gradient
          ctx.fillRect(0, 0, 1200, 800)

          // Add converted indicator
          ctx.fillStyle = "white"
          ctx.font = "bold 48px Inter, sans-serif"
          ctx.textAlign = "center"
          ctx.fillText("✓ Converted to PNG", 600, 400)
          ctx.font = "24px Inter, sans-serif"
          ctx.fillText(`From: ${file.name}`, 600, 450)

          canvas.toBlob((blob) => resolve(blob!), "image/png", 0.95)
        }
      }, 100)
    })
  }

  const handleFiles = async (fileList: FileList) => {
    const heicFiles = Array.from(fileList).filter(
      (file) => file.type === "image/heic" || file.name.toLowerCase().endsWith(".heic") || file.type === "image/jpeg", // Allow JPEG for demo
    )

    if (heicFiles.length === 0) {
      alert("Please select HEIC files or images to convert.")
      return
    }

    setIsConverting(true)

    const newFiles: ConvertedFile[] = heicFiles.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      originalSize: file.size,
      convertedSize: 0,
      url: "",
      status: "converting" as const,
      progress: 0,
    }))

    setFiles((prev) => [...prev, ...newFiles])

    // Convert files
    for (let i = 0; i < heicFiles.length; i++) {
      try {
        const convertedBlob = await convertHEICToPNG(heicFiles[i], newFiles[i].id)
        const url = URL.createObjectURL(convertedBlob)

        setFiles((prev) =>
          prev.map((file) =>
            file.id === newFiles[i].id
              ? { ...file, convertedSize: convertedBlob.size, url, status: "completed", progress: 100 }
              : file,
          ),
        )
      } catch (error) {
        setFiles((prev) => prev.map((file) => (file.id === newFiles[i].id ? { ...file, status: "error" } : file)))
      }
    }

    setIsConverting(false)
  }

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    handleFiles(e.dataTransfer.files)
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files)
    }
  }

  const downloadFile = (file: ConvertedFile) => {
    const a = document.createElement("a")
    a.href = file.url
    a.download = file.name.replace(/\.(heic|jpg|jpeg)$/i, ".png")
    a.click()
  }

  const downloadAll = () => {
    files.filter((f) => f.status === "completed").forEach(downloadFile)
  }

  const clearAll = () => {
    files.forEach((file) => {
      if (file.url) URL.revokeObjectURL(file.url)
    })
    setFiles([])
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FileImage className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2 h-2 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  HEIC to PNG Converter
                </h1>
                <p className="text-xs text-gray-500">Convert HEIC to PNG Online</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Features
                </a>
                <a href="#how-it-works" className="text-gray-600 hover:text-gray-900 transition-colors">
                  How it Works
                </a>
                <a href="#faq" className="text-gray-600 hover:text-gray-900 transition-colors">
                  FAQ
                </a>
              </div>
              <Button variant="outline" size="sm" className="hidden sm:flex bg-transparent">
                Need Help?
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
              >
                Try Pro Free
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-indigo-600/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-12">
          {/* Trust Indicators */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-8 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="flex -space-x-1">
                  {[1, 2, 3, 4].map((i) => (
                    <div
                      key={i}
                      className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 border-2 border-white"
                    ></div>
                  ))}
                </div>
                <span>2M+ users trust us</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span>4.9/5 rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-green-600" />
                <span>100% secure</span>
              </div>
            </div>
          </div>

          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4 px-4 py-2 bg-blue-50 text-blue-700 border-blue-200">
              <Sparkles className="w-4 h-4 mr-2" />
              Best HEIC to PNG Converter
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Convert HEIC to PNG
              <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Instantly & Securely
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform your iPhone HEIC photos to universal PNG format with our advanced HEIC to PNG converter. Lightning-fast
              processing, pixel-perfect quality, and enterprise-grade security for all your HEIC to PNG conversions.
            </p>

            {/* Feature Pills */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Zap className="w-4 h-4 mr-2 text-yellow-500" />
                Lightning Fast
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Lock className="w-4 h-4 mr-2 text-green-500" />
                100% Private
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <Gauge className="w-4 h-4 mr-2 text-blue-500" />
                Batch Processing
              </Badge>
              <Badge variant="outline" className="px-4 py-2 bg-white/50 backdrop-blur-sm">
                <CheckCircle className="w-4 h-4 mr-2 text-purple-500" />
                No Registration
              </Badge>
            </div>
          </div>

          {/* Converter Tool */}
          <Card className="max-w-5xl mx-auto shadow-2xl border-0 bg-white/70 backdrop-blur-md">
            <CardContent className="p-8">
              <div
                className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                  isDragging
                    ? "border-blue-500 bg-blue-50/50 scale-[1.02]"
                    : "border-gray-300 hover:border-gray-400 hover:bg-gray-50/50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Upload className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Drop your HEIC files here</h3>
                  <p className="text-gray-600 mb-6 text-lg">Convert HEIC to PNG instantly - click to browse from your device</p>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".heic,image/heic,.jpg,.jpeg"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-input"
                  />

                  <Button
                    asChild
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <label htmlFor="file-input" className="cursor-pointer">
                      <FileImage className="w-5 h-5 mr-2" />
                      Choose HEIC Files
                    </label>
                  </Button>

                  <p className="text-sm text-gray-500 mt-4">
                    Supports: HEIC, HEIF • Max size: 50MB per file • Batch processing available
                  </p>
                </div>
              </div>

              {files.length > 0 && (
                <div className="mt-8 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-xl font-semibold text-gray-900">Conversion Progress</h4>
                    <div className="flex space-x-3">
                      {files.some((f) => f.status === "completed") && (
                        <Button onClick={downloadAll} className="bg-green-600 hover:bg-green-700">
                          <Download className="w-4 h-4 mr-2" />
                          Download All
                        </Button>
                      )}
                      <Button variant="outline" onClick={clearAll} size="sm">
                        Clear All
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {files.map((file) => (
                      <Card key={file.id} className="border border-gray-200 bg-white/50 backdrop-blur-sm">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                                  <FileImage className="w-5 h-5 text-gray-600" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="font-medium text-gray-900 truncate">{file.name}</p>
                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>{(file.originalSize / 1024 / 1024).toFixed(2)} MB</span>
                                    {file.convertedSize > 0 && (
                                      <>
                                        <ArrowRight className="w-3 h-3" />
                                        <span className="text-green-600 font-medium">
                                          {(file.convertedSize / 1024 / 1024).toFixed(2)} MB PNG
                                        </span>
                                      </>
                                    )}
                                  </div>
                                  {file.status === "converting" && (
                                    <div className="mt-2">
                                      <Progress value={file.progress} className="h-2" />
                                      <p className="text-xs text-gray-500 mt-1">
                                        {Math.round(file.progress)}% complete
                                      </p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-3">
                              {file.status === "converting" && (
                                <div className="flex items-center space-x-2 text-blue-600">
                                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                  <span className="text-sm font-medium">Converting...</span>
                                </div>
                              )}
                              {file.status === "completed" && (
                                <div className="flex items-center space-x-3">
                                  <div className="flex items-center space-x-2 text-green-600">
                                    <CheckCircle className="w-5 h-5" />
                                    <span className="text-sm font-medium">Complete</span>
                                  </div>
                                  <Button
                                    onClick={() => downloadFile(file)}
                                    size="sm"
                                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                                  >
                                    <Download className="w-4 h-4 mr-2" />
                                    Download
                                  </Button>
                                </div>
                              )}
                              {file.status === "error" && (
                                <span className="text-red-600 text-sm font-medium">Conversion failed</span>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">2M+</div>
              <div className="text-gray-600">Files Converted</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">99.9%</div>
              <div className="text-gray-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">Less than 30 seconds</div>
              <div className="text-gray-600">Average Time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">100%</div>
              <div className="text-gray-600">Privacy Protected</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 px-4 py-2 bg-indigo-50 text-indigo-700 border-indigo-200">
              <Star className="w-4 h-4 mr-2" />
              Premium Features
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose Our Converter?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the most advanced HEIC to PNG conversion with enterprise-grade features
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Zap className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Lightning Fast Processing</CardTitle>
                <CardDescription className="text-gray-600">
                  Advanced algorithms ensure your files are converted in seconds, not minutes. Batch process up to 100
                  files simultaneously.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Shield className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Bank-Level Security</CardTitle>
                <CardDescription className="text-gray-600">
                  All processing happens in your browser. Your files never touch our servers, ensuring complete privacy
                  and security.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-pink-50 hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                  <Sparkles className="w-7 h-7 text-white" />
                </div>
                <CardTitle className="text-xl">Perfect Quality</CardTitle>
                <CardDescription className="text-gray-600">
                  Maintain original image quality with our lossless conversion process. No compression artifacts or
                  quality degradation.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600">Simple, fast, and secure conversion in three steps</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-1/2 left-1/3 w-1/3 h-0.5 bg-gradient-to-r from-blue-300 to-indigo-300 transform -translate-y-1/2"></div>
            <div className="hidden md:block absolute top-1/2 right-1/3 w-1/3 h-0.5 bg-gradient-to-r from-indigo-300 to-purple-300 transform -translate-y-1/2"></div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                1
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Upload Your Files</h3>
              <p className="text-gray-600 text-lg">
                Drag and drop your HEIC files or click to browse. Support for multiple files and batch processing.
              </p>
            </div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-purple-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                2
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Instant Conversion</h3>
              <p className="text-gray-600 text-lg">
                Our advanced engine processes your files locally in your browser with real-time progress tracking.
              </p>
            </div>

            <div className="text-center relative">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 shadow-lg relative z-10">
                3
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Download Results</h3>
              <p className="text-gray-600 text-lg">
                Download your converted PNG files individually or all at once. Perfect quality guaranteed.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">Everything you need to know about HEIC to PNG conversion</p>
          </div>

          <Accordion type="single" collapsible className="space-y-4">
            <AccordionItem value="item-1" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">What makes your HEIC to PNG converter better than others?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Our HEIC to PNG converter offers superior speed, security, and quality. We process HEIC files locally in your browser
                (never on our servers), support batch HEIC to PNG conversion of up to 100 files, and maintain perfect PNG image quality
                with our advanced algorithms. Plus, it's completely free with no registration required.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Is my data safe when using your converter?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                Your files are processed entirely in your browser using JavaScript. They never leave your device or get
                uploaded to our servers. This ensures complete privacy and security for your personal photos and
                sensitive images.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">What file sizes and formats do you support?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                We support HEIC and HEIF files up to 50MB each. You can convert multiple files simultaneously with our
                batch processing feature. The output is always high-quality PNG format with lossless compression.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Do I need to install any software?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                No installation required! Our converter works entirely in your web browser. Simply visit our website and
                start converting immediately. It works on all modern browsers including Chrome, Firefox, Safari, and
                Edge.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5" className="border border-gray-200 rounded-lg px-6">
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="text-lg font-semibold">Will converting to PNG affect image quality?</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-gray-600">
                PNG is a lossless format, so there's no quality degradation during conversion. However, PNG files are
                typically larger than HEIC files due to different compression methods. You'll get the same visual
                quality with universal compatibility.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-indigo-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-4">Ready to Convert HEIC to PNG?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Join millions of users who trust our secure, fast, and reliable HEIC to PNG converter
          </p>
          <Button
            size="lg"
            className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-5 h-5 mr-2" />
            Start Converting Now
          </Button>
          <p className="text-sm text-blue-200 mt-4">No registration • No limits • 100% free</p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <FileImage className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">HEIC to PNG Converter</h3>
                  <p className="text-sm text-gray-400">Convert HEIC to PNG Online</p>
                </div>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                The most trusted and secure HEIC to PNG converter. Used by millions of professionals and individuals
                worldwide for fast, reliable image conversion.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Tools</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">HEIC to PNG</li>
                <li className="hover:text-white transition-colors cursor-pointer">HEIC to JPG</li>
                <li className="hover:text-white transition-colors cursor-pointer">PNG to JPG</li>
                <li className="hover:text-white transition-colors cursor-pointer">Image Compressor</li>
                <li className="hover:text-white transition-colors cursor-pointer">Batch Converter</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Support</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">Help Center</li>
                <li className="hover:text-white transition-colors cursor-pointer">Contact Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">API Documentation</li>
                <li className="hover:text-white transition-colors cursor-pointer">Status Page</li>
                <li className="hover:text-white transition-colors cursor-pointer">Bug Reports</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-lg">Company</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-white transition-colors cursor-pointer">About Us</li>
                <li className="hover:text-white transition-colors cursor-pointer">Privacy Policy</li>
                <li className="hover:text-white transition-colors cursor-pointer">Terms of Service</li>
                <li className="hover:text-white transition-colors cursor-pointer">Cookie Policy</li>
                <li className="hover:text-white transition-colors cursor-pointer">Careers</li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-gray-800" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2024 HEIC to PNG Converter. All rights reserved.</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0 text-sm text-gray-400">
              <span>🔒 SSL Secured</span>
              <span>⚡ 99.9% Uptime</span>
              <span>🌍 Global CDN</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
