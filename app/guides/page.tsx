import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowRight, Clock, Users, Star, BookOpen, HelpCircle, BarChart3 } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export const metadata: Metadata = {
  title: 'HEIC Conversion Guides & Tutorials - Complete Learning Center',
  description: 'Comprehensive guides for HEIC file conversion on Windows, Mac, and mobile devices. Learn best practices, troubleshooting, and format comparisons.',
  keywords: 'heic conversion guides, heic tutorials, windows heic guide, mac heic guide, heic help center',
  openGraph: {
    title: 'HEIC Conversion Guides & Tutorials',
    description: 'Complete learning center for HEIC file conversion across all platforms.',
    type: 'website',
    url: 'https://convert-heic.com/guides',
  },
}

const guides = [
  {
    title: '2025 Ultimate Guide: How to Open and Batch Convert HEIC Files on Windows 11/10',
    description: 'Complete Windows guide covering 3 fastest methods to open HEIC files and batch convert to PNG/JPG. Includes official methods, third-party software, and the best online solution.',
    href: '/guides/windows-heic-converter',
    category: 'Windows Guide',
    readTime: '8 min read',
    difficulty: 'Beginner',
    featured: true,
    tags: ['Windows', 'Batch Conversion', 'Step-by-Step']
  },
  {
    title: 'Mac Users Need This Too? How to Convert HEIC to PNG/JPG on macOS',
    description: 'Mac user guide for HEIC conversion including Preview App, Photos App, and best online solutions for cross-platform compatibility.',
    href: '/guides/mac-heic-to-png',
    category: 'Mac Guide',
    readTime: '6 min read',
    difficulty: 'Beginner',
    featured: true,
    tags: ['macOS', 'Preview App', 'Cross-Platform']
  },
  {
    title: 'HEIC vs. JPG vs. PNG: 2025\'s Most Comprehensive Format Comparison',
    description: 'Deep dive comparison of the three major image formats. Learn compression, quality, compatibility, and use cases to choose the right format.',
    href: '/compare/heic-vs-jpg-png',
    category: 'Format Comparison',
    readTime: '10 min read',
    difficulty: 'Intermediate',
    featured: true,
    tags: ['Comparison', 'Technical', 'Quality']
  }
]

const categories = [
  {
    name: 'Platform Guides',
    icon: BookOpen,
    description: 'Step-by-step guides for Windows, Mac, and mobile devices',
    count: 3,
    color: 'bg-blue-50 text-blue-700 border-blue-200'
  },
  {
    name: 'Problem Solving',
    icon: HelpCircle,
    description: 'Troubleshooting common HEIC conversion issues',
    count: 2,
    color: 'bg-green-50 text-green-700 border-green-200'
  },
  {
    name: 'Format Comparisons',
    icon: BarChart3,
    description: 'Technical comparisons and format analysis',
    count: 1,
    color: 'bg-purple-50 text-purple-700 border-purple-200'
  }
]

export default function GuidesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">H</span>
              </div>
              <span className="text-xl font-bold text-gray-900">HEIC to PNG</span>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-gray-600 hover:text-blue-600 transition-colors">
                Converter
              </Link>
              <Link href="/guides" className="text-blue-600 font-medium">
                Guides
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 py-3 text-sm">
            <Link href="/" className="text-blue-600 hover:text-blue-800">Home</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Guides</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            HEIC Conversion Guides & Tutorials
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Master HEIC file conversion with our comprehensive guides. From beginner tutorials to advanced techniques, 
            we've got everything you need to handle HEIC files like a pro.
          </p>
        </div>

        {/* Categories */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {categories.map((category, index) => {
            const IconComponent = category.icon
            return (
              <div key={index} className={`${category.color} border rounded-xl p-6`}>
                <div className="flex items-center mb-3">
                  <IconComponent className="w-6 h-6 mr-3" />
                  <h3 className="text-lg font-semibold">{category.name}</h3>
                </div>
                <p className="text-sm mb-3">{category.description}</p>
                <Badge variant="secondary" className="text-xs">
                  {category.count} {category.count === 1 ? 'guide' : 'guides'}
                </Badge>
              </div>
            )
          })}
        </div>

        {/* Featured Guides */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Guides</h2>
          <div className="grid lg:grid-cols-2 gap-8">
            {guides.filter(guide => guide.featured).map((guide, index) => (
              <article key={index} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <Badge variant="secondary">{guide.category}</Badge>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {guide.readTime}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {guide.difficulty}
                      </Badge>
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                    {guide.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {guide.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {guide.tags.map((tag, tagIndex) => (
                      <Badge key={tagIndex} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <Link href={guide.href}>
                    <Button className="w-full group">
                      Read Guide
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </div>

        {/* All Guides */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">All Guides</h2>
          <div className="space-y-4">
            {guides.map((guide, index) => (
              <article key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Badge variant="secondary">{guide.category}</Badge>
                      <span className="text-sm text-gray-500 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {guide.readTime}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {guide.difficulty}
                      </Badge>
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {guide.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-3">
                      {guide.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {guide.tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <Link href={guide.href}>
                    <Button variant="outline" className="ml-6 group">
                      Read
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-8 text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Ready to Convert Your HEIC Files?</h2>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Put your knowledge to practice with our free online HEIC converter. Fast, secure, and works entirely in your browser.
          </p>
          <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
            <Link href="/">Start Converting Now</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
