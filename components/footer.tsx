import Link from 'next/link'
import { FileImage } from 'lucide-react'
import { Separator } from '@/components/ui/separator'

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-5 gap-8">
          <div className="md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <FileImage className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">HEIC to PNG Converter</h3>
                <p className="text-sm text-gray-400">Convert HEIC to PNG Online</p>
              </div>
            </div>
            <p className="text-gray-400 mb-6 leading-relaxed">
              The most trusted and secure HEIC to PNG converter. Used by millions of professionals and individuals
              worldwide for fast, reliable image conversion.
            </p>
            <div className="flex space-x-4">
              <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                <span className="text-sm font-bold">f</span>
              </div>
              <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                <span className="text-sm font-bold">t</span>
              </div>
              <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                <span className="text-sm font-bold">in</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">Features</h4>
            <ul className="space-y-3 text-gray-400">
              <li><Link href="/?format=png" className="hover:text-white transition-colors">HEIC to PNG Converter</Link></li>
              <li><Link href="/?format=jpg" className="hover:text-white transition-colors">HEIC to JPG Converter</Link></li>
              <li><Link href="/#features" className="hover:text-white transition-colors">Batch Processing</Link></li>
              <li><Link href="/#features" className="hover:text-white transition-colors">Privacy Protection</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">Resources</h4>
            <ul className="space-y-3 text-gray-400">
              <li><Link href="/guides" className="hover:text-white transition-colors">User Guides</Link></li>
              <li><Link href="/guides/windows-heic-converter" className="hover:text-white transition-colors">Windows Guide</Link></li>
              <li><Link href="/guides/mac-heic-to-png" className="hover:text-white transition-colors">Mac Guide</Link></li>
              <li><Link href="/compare/heic-vs-jpg-png" className="hover:text-white transition-colors">Format Comparison</Link></li>
              <li><Link href="/#faq" className="hover:text-white transition-colors">FAQ</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">Legal</h4>
            <ul className="space-y-3 text-gray-400">
              <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
            </ul>
          </div>
        </div>

        <Separator className="my-8 bg-gray-800" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">© 2024 HEIC to PNG Converter. All rights reserved.</p>
          <div className="flex items-center space-x-6 mt-4 md:mt-0 text-sm text-gray-400">
            <span>🔒 SSL Secured</span>
            <span>⚡ 99.9% Uptime</span>
            <span>🌍 Global CDN</span>
          </div>
        </div>
      </div>
    </footer>
  )
}
