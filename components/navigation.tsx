'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { FileImage, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface NavigationProps {
  onHistoryClick?: () => void
  historyCount?: number
  showHistoryButton?: boolean
}

export function Navigation({ onHistoryClick, historyCount = 0, showHistoryButton = false }: NavigationProps) {
  const pathname = usePathname()

  return (
    <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
              <FileImage className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">HEIC to PNG</span>
          </Link>
          
          <div className="flex items-center space-x-6">
            <Link 
              href="/" 
              className={`transition-colors ${
                pathname === '/' ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600'
              }`}
            >
              Converter
            </Link>
            <Link 
              href="/guides" 
              className={`transition-colors ${
                pathname.startsWith('/guides') ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600'
              }`}
            >
              Guides
            </Link>
            
            {showHistoryButton && (
              <Button
                variant="outline"
                size="sm"
                onClick={onHistoryClick}
                className="hidden sm:flex bg-transparent"
              >
                <Clock className="w-4 h-4 mr-2" />
                History ({historyCount})
              </Button>
            )}
            
            <Button 
              variant="outline" 
              size="sm" 
              className="hidden sm:flex bg-transparent"
              onClick={() => {
                const faqSection = document.getElementById('faq')
                if (faqSection) {
                  faqSection.scrollIntoView({ behavior: 'smooth' })
                } else {
                  // If not on homepage, go to homepage with FAQ anchor
                  window.location.href = '/#faq'
                }
              }}
            >
              Need Help?
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}
